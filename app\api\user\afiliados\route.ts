import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("user_id")

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: "user_id é obrigatório"
      }, { status: 400 })
    }

    await initializeDatabase()

    // Buscar afiliados do usuário
    const afiliados = await executeQuery(`
      SELECT 
        u.id,
        u.nome,
        u.email,
        u.data_cadastro,
        u.status,
        COALESCE(SUM(b.valor_total), 0) as total_apostado,
        COALESCE(SUM(c.valor), 0) as comissao_gerada
      FROM usuarios u
      LEFT JOIN bilhetes b ON u.id = b.usuario_id AND b.status = 'pago'
      LEFT JOIN comissoes c ON u.id = c.afiliado_id
      WHERE u.afiliado_id = ?
      GROUP BY u.id, u.nome, u.email, u.data_cadastro, u.status
      ORDER BY u.data_cadastro DESC
    `, [userId])

    // Calcular comissão do mês atual
    const comissaoMes = await executeQuery(`
      SELECT COALESCE(SUM(valor), 0) as total
      FROM comissoes 
      WHERE afiliado_id IN (
        SELECT id FROM usuarios WHERE afiliado_id = ?
      )
      AND MONTH(created_at) = MONTH(CURRENT_DATE())
      AND YEAR(created_at) = YEAR(CURRENT_DATE())
    `, [userId])

    const afiliadosFormatados = afiliados.map((afiliado: any) => ({
      id: afiliado.id,
      nome: afiliado.nome,
      email: afiliado.email,
      data_cadastro: afiliado.data_cadastro,
      status: afiliado.status,
      total_apostado: parseFloat(afiliado.total_apostado || 0),
      comissao_gerada: parseFloat(afiliado.comissao_gerada || 0)
    }))

    return NextResponse.json({
      success: true,
      afiliados: afiliadosFormatados,
      comissao_mes: parseFloat(comissaoMes[0]?.total || 0)
    })

  } catch (error) {
    console.error("❌ Erro ao buscar afiliados:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      afiliados: [],
      comissao_mes: 0
    }, { status: 500 })
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { initializeDatabase, executeQuery } from '@/lib/database-config'

export const dynamic = 'force-dynamic'

// Função para buscar histórico completo de todas as fontes
async function buscarHistoricoCompleto(userId: number) {
  const transacoes: any[] = []

  try {
    // 1. Buscar transações da tabela saldo_transacoes (histórico principal)
    const saldoTransacoes = await executeQuery(`
      SELECT
        id, tipo, valor, saldo_anterior, saldo_posterior,
        descricao, bilhete_id, transaction_id, status, created_at,
        'saldo_transacoes' as fonte
      FROM saldo_transacoes
      WHERE usuario_id = ?
      ORDER BY created_at DESC
    `, [userId])

    if (saldoTransacoes && saldoTransacoes.length > 0) {
      transacoes.push(...saldoTransacoes)
    }

    // 2. Buscar depósitos PIX confirmados que não estão no histórico
    const depositosPix = await executeQuery(`
      SELECT
        d.id, d.valor, d.transaction_id, d.status, d.created_at,
        d.client_name, 'deposito_pix' as fonte
      FROM depositos_pix d
      WHERE d.usuario_id = ?
      AND d.status = 'pago'
      AND NOT EXISTS (
        SELECT 1 FROM saldo_transacoes st
        WHERE st.transaction_id = d.transaction_id
        AND st.tipo = 'deposito'
        AND st.usuario_id = ?
      )
      ORDER BY d.created_at DESC
    `, [userId, userId])

    // Adicionar depósitos PIX ao histórico
    if (depositosPix && depositosPix.length > 0) {
      for (const dep of depositosPix) {
        transacoes.push({
          id: `dep_${dep.id}`,
          tipo: 'deposito',
          valor: parseFloat(dep.valor),
          saldoAnterior: 0, // Será calculado depois
          saldoPosterior: 0, // Será calculado depois
          descricao: `Depósito PIX - ${dep.client_name}`,
          bilhete_id: null,
          transaction_id: dep.transaction_id,
          status: 'confirmado',
          created_at: dep.created_at,
          fonte: 'deposito_pix'
        })
      }
    }

    // 3. Buscar bilhetes pagos que não estão no histórico
    const bilhetesPagos = await executeQuery(`
      SELECT
        b.id, b.codigo, b.valor_total, b.usuario_nome, b.created_at,
        'bilhete' as fonte
      FROM bilhetes b
      WHERE b.usuario_id = ?
      AND b.status = 'pago'
      AND NOT EXISTS (
        SELECT 1 FROM saldo_transacoes st
        WHERE st.bilhete_id = b.id
        AND st.tipo = 'compra_bilhete'
        AND st.usuario_id = ?
      )
      ORDER BY b.created_at DESC
    `, [userId, userId])

    // Adicionar bilhetes pagos ao histórico
    if (bilhetesPagos && bilhetesPagos.length > 0) {
      for (const bilhete of bilhetesPagos) {
        transacoes.push({
          id: `bil_${bilhete.id}`,
          tipo: 'compra_bilhete',
          valor: parseFloat(bilhete.valor_total),
          saldoAnterior: 0, // Será calculado depois
          saldoPosterior: 0, // Será calculado depois
          descricao: `Compra de Bilhete #${bilhete.codigo} - ${bilhete.usuario_nome}`,
          bilhete_id: bilhete.id,
          transaction_id: null,
          status: 'confirmado',
          created_at: bilhete.created_at,
          fonte: 'bilhete'
        })
      }
    }

    // 4. Buscar saques (se existir tabela de saques)
    try {
      const saques = await executeQuery(`
        SELECT
          s.id, s.valor, s.status, s.created_at, s.transaction_id,
          'saque' as fonte
        FROM saques s
        WHERE s.usuario_id = ?
        AND s.status IN ('aprovado', 'processado')
        AND NOT EXISTS (
          SELECT 1 FROM saldo_transacoes st
          WHERE st.transaction_id = s.transaction_id
          AND st.tipo = 'saque'
          AND st.usuario_id = ?
        )
        ORDER BY s.created_at DESC
      `, [userId, userId])

      // Adicionar saques ao histórico
      if (saques && saques.length > 0) {
        for (const saque of saques) {
          transacoes.push({
            id: `saq_${saque.id}`,
            tipo: 'saque',
            valor: parseFloat(saque.valor),
            saldoAnterior: 0, // Será calculado depois
            saldoPosterior: 0, // Será calculado depois
            descricao: `Saque PIX - ${saque.transaction_id}`,
            bilhete_id: null,
            transaction_id: saque.transaction_id,
            status: saque.status === 'aprovado' ? 'confirmado' : 'pendente',
            created_at: saque.created_at,
            fonte: 'saque'
          })
        }
      }
    } catch (error) {
      // Tabela de saques pode não existir ainda
      console.log('ℹ️ Tabela de saques não encontrada ou vazia')
    }

    // Ordenar todas as transações por data (mais recentes primeiro)
    transacoes.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

    // Limitar a 50 transações mais recentes
    return transacoes.slice(0, 50)

  } catch (error) {
    console.error('❌ Erro ao buscar histórico completo:', error)
    return []
  }
}

// Função para atualizar saldo do usuário
async function atualizarSaldo(
  userId: number, 
  tipo: string, 
  valor: number, 
  descricao: string, 
  bilheteId?: number,
  transactionId?: string
) {
  try {
    // Buscar saldo atual
    const usuario = await executeQuery(`
      SELECT saldo FROM usuarios WHERE id = ?
    `, [userId])

    if (!usuario || usuario.length === 0) {
      throw new Error('Usuário não encontrado')
    }

    const saldoAnterior = parseFloat(usuario[0].saldo) || 0
    let saldoPosterior = saldoAnterior

    // Calcular novo saldo baseado no tipo de transação
    switch (tipo) {
      case 'deposito':
      case 'premio':
      case 'estorno':
      case 'bonus':
        saldoPosterior = saldoAnterior + valor
        break
      case 'compra_bilhete':
      case 'saque':
        saldoPosterior = saldoAnterior - valor
        if (saldoPosterior < 0) {
          throw new Error('Saldo insuficiente')
        }
        break
      default:
        throw new Error('Tipo de transação inválido')
    }

    // Iniciar transação
    await executeQuery('START TRANSACTION')

    try {
      // Atualizar saldo do usuário
      await executeQuery(`
        UPDATE usuarios SET saldo = ? WHERE id = ?
      `, [saldoPosterior, userId])

      // Registrar transação
      const transacaoResult = await executeQuery(`
        INSERT INTO saldo_transacoes (
          usuario_id, tipo, valor, saldo_anterior, saldo_posterior,
          descricao, bilhete_id, transaction_id, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'confirmado')
      `, [
        userId, tipo, valor, saldoAnterior, saldoPosterior,
        descricao, bilheteId || null, transactionId || null
      ])

      // Confirmar transação
      await executeQuery('COMMIT')

      console.log(`✅ Saldo atualizado para usuário ${userId}:`, {
        tipo,
        valor,
        saldoAnterior,
        saldoPosterior,
        transacaoId: (transacaoResult as any).insertId
      })

      return {
        success: true,
        saldoAnterior,
        saldoPosterior,
        transacaoId: (transacaoResult as any).insertId
      }

    } catch (error) {
      await executeQuery('ROLLBACK')
      throw error
    }

  } catch (error) {
    console.error('❌ Erro ao atualizar saldo:', error)
    throw error
  }
}

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'user_id é obrigatório'
      }, { status: 400 })
    }

    // Buscar saldo atual do usuário
    const usuario = await executeQuery(`
      SELECT id, nome, email, saldo FROM usuarios WHERE id = ?
    `, [userId])

    if (!usuario || usuario.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Usuário não encontrado'
      }, { status: 404 })
    }

    // Buscar histórico completo de transações de múltiplas fontes
    const transacoes = await buscarHistoricoCompleto(parseInt(userId))

    const saldoAtual = parseFloat(usuario[0].saldo) || 0

    console.log(`💰 Saldo consultado para usuário ${userId}: R$ ${saldoAtual.toFixed(2)}`)

    return NextResponse.json({
      success: true,
      usuario: {
        id: usuario[0].id,
        nome: usuario[0].nome,
        email: usuario[0].email,
        saldo: saldoAtual
      },
      transacoes: (transacoes || []).map((t: any) => ({
        id: t.id,
        tipo: t.tipo,
        valor: parseFloat(t.valor),
        saldoAnterior: parseFloat(t.saldo_anterior),
        saldoPosterior: parseFloat(t.saldo_posterior),
        descricao: t.descricao,
        bilheteId: t.bilhete_id,
        transactionId: t.transaction_id,
        status: t.status,
        data: new Date(t.created_at).toLocaleString('pt-BR')
      }))
    })

  } catch (error) {
    console.error('❌ Erro ao consultar saldo:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro interno do servidor'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const {
      user_id,
      tipo,
      valor,
      descricao,
      bilhete_id,
      transaction_id
    } = body

    console.log('💰 Processando transação de saldo:', {
      user_id, tipo, valor, descricao, bilhete_id, transaction_id
    })

    // Validações
    if (!user_id || !tipo || !valor || !descricao) {
      return NextResponse.json({
        success: false,
        error: 'Dados obrigatórios não fornecidos'
      }, { status: 400 })
    }

    if (!['deposito', 'compra_bilhete', 'premio', 'estorno', 'bonus'].includes(tipo)) {
      return NextResponse.json({
        success: false,
        error: 'Tipo de transação inválido'
      }, { status: 400 })
    }

    if (valor <= 0) {
      return NextResponse.json({
        success: false,
        error: 'Valor deve ser maior que zero'
      }, { status: 400 })
    }

    // Atualizar saldo
    const resultado = await atualizarSaldo(
      user_id, tipo, valor, descricao, bilhete_id, transaction_id
    )

    return NextResponse.json({
      success: true,
      message: 'Transação processada com sucesso',
      ...resultado
    })

  } catch (error) {
    console.error('❌ Erro ao processar transação:', error)
    
    let errorMessage = 'Erro interno do servidor'
    if (error instanceof Error) {
      if (error.message === 'Saldo insuficiente') {
        errorMessage = 'Saldo insuficiente para esta operação'
      } else if (error.message === 'Usuário não encontrado') {
        errorMessage = 'Usuário não encontrado'
      }
    }

    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 })
  }
}

// Exportar função para uso em outras APIs
export { atualizarSaldo }

#!/usr/bin/env node

/**
 * Script para corrigir problemas nos bolões e jogos
 */

const mysql = require('mysql2/promise');

// Configuração do banco de dados
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'sistema-bolao-top',
  charset: 'utf8mb4'
};

async function fixBolaoJogos() {
  let connection;
  
  try {
    console.log('🔧 Conectando ao banco de dados...');
    connection = await mysql.createConnection(dbConfig);
    
    console.log('✅ Conexão estabelecida com sucesso');
    
    // Verificar estrutura das tabelas
    console.log('🔍 Verificando estrutura das tabelas...');
    
    // Verificar se a tabela boloes existe
    const [boloes] = await connection.execute(`
      SELECT COUNT(*) as count FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'boloes'
    `, [dbConfig.database]);
    
    if (boloes[0].count === 0) {
      console.log('⚠️ Tabela boloes não encontrada, criando...');
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS boloes (
          id INT AUTO_INCREMENT PRIMARY KEY,
          nome VARCHAR(255) NOT NULL,
          descricao TEXT,
          valor_entrada DECIMAL(10,2) DEFAULT 0.00,
          max_participantes INT DEFAULT 100,
          data_inicio DATETIME,
          data_fim DATETIME,
          status ENUM('ativo', 'inativo', 'finalizado') DEFAULT 'ativo',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
      `);
      console.log('✅ Tabela boloes criada');
    }
    
    // Verificar se a tabela jogos existe
    const [jogos] = await connection.execute(`
      SELECT COUNT(*) as count FROM information_schema.tables 
      WHERE table_schema = ? AND table_name = 'jogos'
    `, [dbConfig.database]);
    
    if (jogos[0].count === 0) {
      console.log('⚠️ Tabela jogos não encontrada, criando...');
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS jogos (
          id INT AUTO_INCREMENT PRIMARY KEY,
          time_casa VARCHAR(255) NOT NULL,
          time_visitante VARCHAR(255) NOT NULL,
          data_jogo DATETIME NOT NULL,
          campeonato VARCHAR(255),
          status ENUM('agendado', 'ao_vivo', 'finalizado', 'cancelado') DEFAULT 'agendado',
          placar_casa INT DEFAULT 0,
          placar_visitante INT DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
      `);
      console.log('✅ Tabela jogos criada');
    }
    
    // Verificar integridade dos dados
    console.log('🔍 Verificando integridade dos dados...');
    
    const [boloesCount] = await connection.execute('SELECT COUNT(*) as count FROM boloes');
    const [jogosCount] = await connection.execute('SELECT COUNT(*) as count FROM jogos');
    
    console.log(`📊 Estatísticas:`);
    console.log(`   - Bolões: ${boloesCount[0].count}`);
    console.log(`   - Jogos: ${jogosCount[0].count}`);
    
    console.log('✅ Verificação concluída com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro durante a correção:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Executar o script
if (require.main === module) {
  fixBolaoJogos()
    .then(() => {
      console.log('🎉 Script executado com sucesso!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Erro fatal:', error);
      process.exit(1);
    });
}

module.exports = { fixBolaoJogos };

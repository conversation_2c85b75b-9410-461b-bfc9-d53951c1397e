<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Modal QR Code</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .instructions {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Teste do Modal QR Code PIX</h1>
        
        <div class="instructions">
            <h3>📋 Instruções de Teste:</h3>
            <ol>
                <li><strong>Abra o sistema:</strong> <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></li>
                <li><strong>Clique em:</strong> [🟢 Depósito] no header</li>
                <li><strong>Digite um valor:</strong> Ex: 10</li>
                <li><strong>Clique em:</strong> [Gerar PIX]</li>
                <li><strong>Modal QR Code abrirá:</strong> Com funcionalidade de copia e cola</li>
            </ol>
        </div>

        <h2>🧪 Testes Automatizados</h2>
        
        <button class="btn" onclick="testPixAPI()">
            🔧 Testar API PIX
        </button>
        
        <button class="btn" onclick="testWalletDeposit()">
            💰 Testar Depósito Carteira
        </button>
        
        <button class="btn btn-success" onclick="openSystemInNewTab()">
            🚀 Abrir Sistema
        </button>

        <div id="status"></div>
        <div id="results"></div>

        <h2>📊 Funcionalidades do Modal QR Code</h2>
        <ul>
            <li>✅ <strong>QR Code Visual:</strong> Imagem do QR Code gerada pela API</li>
            <li>✅ <strong>Código Copia e Cola:</strong> Campo com código PIX completo</li>
            <li>✅ <strong>Botão Copiar:</strong> Copia código para área de transferência</li>
            <li>✅ <strong>Feedback Visual:</strong> Ícone muda quando copiado</li>
            <li>✅ <strong>Timer Expiração:</strong> Countdown em tempo real</li>
            <li>✅ <strong>Cores Dinâmicas:</strong> Azul → Amarelo → Vermelho</li>
            <li>✅ <strong>Instruções Claras:</strong> Como pagar passo a passo</li>
            <li>✅ <strong>Simulação Pagamento:</strong> Botão para testar</li>
            <li>✅ <strong>Integração Completa:</strong> Atualiza saldo automaticamente</li>
            <li>✅ <strong>Design Responsivo:</strong> Funciona em desktop e mobile</li>
        </ul>

        <h2>🎨 Preview do Modal</h2>
        <div class="code-block">
┌─────────────────────────────────────────────────────────────────────────────────────────┐
│ 🔲 Pagamento PIX                                                                    ❌   │
├─────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                         │
│                           Valor a pagar                                                │
│                            R$ 10,00                                                    │
│                                                                                         │
│                    ⏰ Expira em: 14:58                                                  │
│                                                                                         │
│                    ┌─────────────────────┐                                             │
│                    │                     │                                             │
│                    │    [QR CODE IMG]    │                                             │
│                    │                     │                                             │
│                    └─────────────────────┘                                             │
│                 Escaneie o QR Code com seu app de banco                                │
│                                                                                         │
│  Código PIX (Copia e Cola)                                    [📋 Copiar]             │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐   │
│  │ 00020126580014BR.GOV.BCB.PIX0136...                                        📋 │   │
│  └─────────────────────────────────────────────────────────────────────────────────┘   │
│                                                                                         │
│  ℹ️ Como pagar:                                                                        │
│  1. Abra seu app do banco                                                              │
│  2. Escaneie o QR Code ou cole o código PIX                                           │
│  3. Confirme o pagamento                                                               │
│  4. Aguarde a confirmação automática                                                   │
│                                                                                         │
│  [✅ Simular Pagamento Confirmado]                                                     │
│  [⚪ Fechar]                                                                           │
│                                                                                         │
│                          ID: pixi_01k118ybv3eh7amecayxn7gpy9                          │
└─────────────────────────────────────────────────────────────────────────────────────────┘
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function showResults(data) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<div class="code-block">${JSON.stringify(data, null, 2)}</div>`;
        }

        async function testPixAPI() {
            showStatus('🔄 Testando API PIX...', 'info');
            
            try {
                const response = await fetch('http://localhost:3000/api/pix/qrcode', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        value: 10,
                        description: 'Teste Modal QR Code',
                        client_name: 'Teste Usuario',
                        client_email: '<EMAIL>',
                        client_document: '12345678901',
                        qrcode_image: true
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    showStatus('✅ API PIX funcionando! QR Code gerado com sucesso.', 'success');
                    showResults(data);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showStatus(`❌ Erro na API PIX: ${error.message}`, 'error');
            }
        }

        async function testWalletDeposit() {
            showStatus('🔄 Testando Depósito na Carteira...', 'info');
            
            try {
                const response = await fetch('http://localhost:3000/api/wallet/deposit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: 27,
                        valor: 10,
                        client_name: 'Teste Usuario',
                        client_email: '<EMAIL>',
                        client_document: '12345678901'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    showStatus('✅ Depósito criado! Modal QR Code deve abrir no sistema.', 'success');
                    showResults(data);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showStatus(`❌ Erro no depósito: ${error.message}`, 'error');
            }
        }

        function openSystemInNewTab() {
            window.open('http://localhost:3000', '_blank');
            showStatus('🚀 Sistema aberto em nova aba! Teste o modal QR Code.', 'success');
        }

        // Auto-teste ao carregar a página
        window.onload = function() {
            showStatus('🎯 Página de teste carregada. Use os botões acima para testar.', 'info');
        };
    </script>
</body>
</html>

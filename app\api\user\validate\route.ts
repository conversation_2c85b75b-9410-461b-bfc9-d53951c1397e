import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuerySingle } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("user_id")

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: "user_id é obrigatório"
      }, { status: 400 })
    }

    await initializeDatabase()

    // Buscar usuário no banco
    const user = await executeQuerySingle(
      `SELECT 
        id, nome, email, telefone, cpf_cnpj, tipo, status, saldo, data_cadastro
       FROM usuarios 
       WHERE id = ? AND status = 'ativo'`,
      [userId]
    )

    if (!user) {
      return NextResponse.json({
        success: false,
        error: "Usuário não encontrado ou inativo"
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        nome: user.nome,
        email: user.email,
        telefone: user.telefone,
        cpf_cnpj: user.cpf_cnpj,
        tipo: user.tipo,
        status: user.status,
        saldo: parseFloat(user.saldo || 0),
        data_cadastro: user.data_cadastro
      }
    })

  } catch (error) {
    console.error("❌ Erro ao validar usuário:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor"
    }, { status: 500 })
  }
}

import { type NextRequest, NextResponse } from "next/server"
import { initializeDatabase } from "@/lib/database-config"
import { updateAfiliado, deleteAfiliado } from "@/lib/database.js"

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await initializeDatabase()

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: "ID inválido" },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { nome, email, telefone, percentual_comissao, status, senha } = body

    // Validações
    if (email && !email.includes("@")) {
      return NextResponse.json(
        { error: "Email inválido" },
        { status: 400 }
      )
    }

    if (percentual_comissao !== undefined && (percentual_comissao < 0 || percentual_comissao > 100)) {
      return NextResponse.json(
        { error: "Percentual de comissão deve estar entre 0 e 100" },
        { status: 400 }
      )
    }

    if (status && !["ativo", "inativo", "bloqueado"].includes(status)) {
      return NextResponse.json(
        { error: "Status inválido" },
        { status: 400 }
      )
    }

    const updateData: any = {}
    if (nome) updateData.nome = nome
    if (email) updateData.email = email
    if (telefone !== undefined) updateData.telefone = telefone
    if (percentual_comissao !== undefined) updateData.percentual_comissao = percentual_comissao
    if (status) updateData.status = status
    if (senha) updateData.senha = senha

    await updateAfiliado(id, updateData)

    return NextResponse.json(
      { 
        success: true, 
        message: "Afiliado atualizado com sucesso"
      }
    )
  } catch (error: any) {
    console.error("Erro ao atualizar afiliado:", error)
    
    if (error.message.includes("já cadastrado")) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await initializeDatabase()

    const id = parseInt(params.id)
    if (isNaN(id)) {
      return NextResponse.json(
        { error: "ID inválido" },
        { status: 400 }
      )
    }

    await deleteAfiliado(id)

    return NextResponse.json(
      { 
        success: true, 
        message: "Afiliado excluído com sucesso"
      }
    )
  } catch (error: any) {
    console.error("Erro ao excluir afiliado:", error)
    
    return NextResponse.json(
      { error: "Erro interno do servidor" },
      { status: 500 }
    )
  }
}

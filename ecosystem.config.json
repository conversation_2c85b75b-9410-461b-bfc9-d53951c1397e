{"apps": [{"name": "sistema-bolao-prod", "script": "node_modules/.bin/next", "args": "start", "cwd": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs", "instances": 1, "exec_mode": "fork", "env": {"NODE_ENV": "development", "PORT": 3000}, "env_production": {"NODE_ENV": "production", "PORT": 3000, "DB_HOST": "localhost", "DB_USER": "ouroemunew_user", "DB_PASSWORD": "Ouro@2024!", "DB_NAME": "sistema-bolao-top"}, "log_file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\logs\\combined.log", "out_file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\logs\\out.log", "error_file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\logs\\error.log", "log_date_format": "YYYY-MM-DD HH:mm:ss Z", "merge_logs": true, "max_memory_restart": "1G", "node_args": "--max-old-space-size=1024", "restart_delay": 4000, "max_restarts": 10, "min_uptime": "10s", "kill_timeout": 5000, "wait_ready": true, "listen_timeout": 8000}]}
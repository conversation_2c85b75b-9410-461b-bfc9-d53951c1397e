import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuerySingle, executeQuery } from "@/lib/database-config"
import bcrypt from 'bcryptjs'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("user_id")

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: "user_id é obrigatório"
      }, { status: 400 })
    }

    await initializeDatabase()

    // Buscar perfil completo do usuário
    const profile = await executeQuerySingle(`
      SELECT 
        id, nome, email, telefone, cpf_cnpj, endereco, 
        data_nascimento, data_cadastro, status, tipo, saldo
      FROM usuarios 
      WHERE id = ?
    `, [userId])

    if (!profile) {
      return NextResponse.json({
        success: false,
        error: "Usuário não encontrado"
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      profile: {
        id: profile.id,
        nome: profile.nome,
        email: profile.email,
        telefone: profile.telefone || "",
        cpf_cnpj: profile.cpf_cnpj || "",
        endereco: profile.endereco || "",
        data_nascimento: profile.data_nascimento || "",
        data_cadastro: profile.data_cadastro,
        status: profile.status,
        tipo: profile.tipo,
        saldo: parseFloat(profile.saldo || 0)
      }
    })

  } catch (error) {
    console.error("❌ Erro ao buscar perfil:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor"
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("user_id")
    const body = await request.json()

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: "user_id é obrigatório"
      }, { status: 400 })
    }

    const { nome, telefone, endereco, senha_atual, nova_senha } = body

    await initializeDatabase()

    // Verificar se usuário existe
    const user = await executeQuerySingle(
      "SELECT id, senha_hash FROM usuarios WHERE id = ?",
      [userId]
    )

    if (!user) {
      return NextResponse.json({
        success: false,
        error: "Usuário não encontrado"
      }, { status: 404 })
    }

    // Se está alterando senha, verificar senha atual
    if (nova_senha && senha_atual) {
      const senhaValida = await bcrypt.compare(senha_atual, user.senha_hash)
      if (!senhaValida) {
        return NextResponse.json({
          success: false,
          error: "Senha atual incorreta"
        }, { status: 400 })
      }
    }

    // Preparar dados para atualização
    let updateQuery = "UPDATE usuarios SET nome = ?, telefone = ?, endereco = ?"
    let updateParams = [nome, telefone, endereco]

    // Se há nova senha, incluir no update
    if (nova_senha && senha_atual) {
      const novaSenhaHash = await bcrypt.hash(nova_senha, 10)
      updateQuery += ", senha_hash = ?"
      updateParams.push(novaSenhaHash)
    }

    updateQuery += " WHERE id = ?"
    updateParams.push(userId)

    // Executar atualização
    await executeQuery(updateQuery, updateParams)

    return NextResponse.json({
      success: true,
      message: "Perfil atualizado com sucesso"
    })

  } catch (error) {
    console.error("❌ Erro ao atualizar perfil:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor"
    }, { status: 500 })
  }
}

'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  X,
  CreditCard,
  Calendar,
  DollarSign,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  Search,
  Filter
} from 'lucide-react'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface Pagamento {
  id: number
  tipo: 'deposito' | 'bilhete' | 'saque'
  valor: number
  status: 'pendente' | 'aprovado' | 'rejeitado' | 'processando'
  descricao: string
  data: string
  transactionId?: string
}

interface MeusPagamentosModalProps {
  isOpen: boolean
  onClose: () => void
  usuario: Usuario
}

export function MeusPagamentosModal({ isOpen, onClose, usuario }: MeusPagamentosModalProps) {
  const [pagamentos, setPagamentos] = useState<Pagamento[]>([])
  const [loading, setLoading] = useState(false)
  const [filtro, setFiltro] = useState<'todos' | 'deposito' | 'bilhete' | 'saque'>('todos')

  useEffect(() => {
    if (isOpen) {
      carregarPagamentos()
    }
  }, [isOpen])

  const carregarPagamentos = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/user/pagamentos?user_id=${usuario.id}`)
      const data = await response.json()

      if (data.success && data.pagamentos) {
        // Mapear dados da API para o formato esperado
        const pagamentosFormatados: Pagamento[] = data.pagamentos.map((pagamento: any) => ({
          id: pagamento.id,
          tipo: pagamento.tipo || 'deposito',
          valor: parseFloat(pagamento.amount || pagamento.valor || 0),
          status: pagamento.status,
          descricao: pagamento.descricao || pagamento.description || 'Transação',
          data: new Date(pagamento.created_at || pagamento.data).toLocaleString('pt-BR'),
          transactionId: pagamento.transaction_id || pagamento.order_id
        }))

        setPagamentos(pagamentosFormatados)
      } else {
        console.error('Erro ao carregar pagamentos:', data.error)
        setPagamentos([])
      }
    } catch (error) {
      console.error('Erro ao carregar pagamentos:', error)
      setPagamentos([])
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'aprovado':
        return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'pendente':
        return <Clock className="h-4 w-4 text-yellow-400" />
      case 'processando':
        return <Clock className="h-4 w-4 text-blue-400" />
      case 'rejeitado':
        return <XCircle className="h-4 w-4 text-red-400" />
      default:
        return <AlertCircle className="h-4 w-4 text-slate-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    const variants = {
      aprovado: 'bg-green-600 text-white',
      pendente: 'bg-yellow-600 text-white',
      processando: 'bg-blue-600 text-white',
      rejeitado: 'bg-red-600 text-white'
    }

    const labels = {
      aprovado: 'Aprovado',
      pendente: 'Pendente',
      processando: 'Processando',
      rejeitado: 'Rejeitado'
    }

    return (
      <Badge className={`${variants[status as keyof typeof variants]} text-xs`}>
        {labels[status as keyof typeof labels]}
      </Badge>
    )
  }

  const getTipoIcon = (tipo: string) => {
    switch (tipo) {
      case 'deposito':
        return <DollarSign className="h-4 w-4 text-green-400" />
      case 'bilhete':
        return <CreditCard className="h-4 w-4 text-blue-400" />
      case 'saque':
        return <DollarSign className="h-4 w-4 text-orange-400" />
      default:
        return <CreditCard className="h-4 w-4 text-slate-400" />
    }
  }

  const pagamentosFiltrados = filtro === 'todos' 
    ? pagamentos 
    : pagamentos.filter(p => p.tipo === filtro)

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-800 rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden border border-slate-600">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-slate-600">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center">
              <CreditCard className="h-5 w-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Meus Pagamentos</h2>
              <p className="text-sm text-slate-300">
                {usuario.nome} - Saldo: R$ {usuario.saldo.toFixed(2).replace('.', ',')}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-slate-400 hover:text-white hover:bg-slate-600"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Filtros */}
        <div className="p-4 border-b border-slate-600 bg-slate-700">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setFiltro('todos')}
              style={{
                backgroundColor: filtro === 'todos' ? '#2563eb' : 'rgb(100, 116, 139)',
                color: filtro === 'todos' ? '#ffffff' : '#000000',
                border: filtro === 'todos' ? 'none' : '1px solid #64748b',
                padding: '6px 12px',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (filtro !== 'todos') {
                  e.currentTarget.style.backgroundColor = '#475569'
                  e.currentTarget.style.color = '#ffffff'
                } else {
                  e.currentTarget.style.backgroundColor = '#1d4ed8'
                }
              }}
              onMouseLeave={(e) => {
                if (filtro !== 'todos') {
                  e.currentTarget.style.backgroundColor = 'rgb(100, 116, 139)'
                  e.currentTarget.style.color = '#000000'
                } else {
                  e.currentTarget.style.backgroundColor = '#2563eb'
                }
              }}
            >
              Todos
            </button>
            <button
              onClick={() => setFiltro('deposito')}
              style={{
                backgroundColor: filtro === 'deposito' ? '#2563eb' : 'rgb(100, 116, 139)',
                color: filtro === 'deposito' ? '#ffffff' : '#000000',
                border: filtro === 'deposito' ? 'none' : '1px solid #64748b',
                padding: '6px 12px',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (filtro !== 'deposito') {
                  e.currentTarget.style.backgroundColor = '#475569'
                  e.currentTarget.style.color = '#ffffff'
                } else {
                  e.currentTarget.style.backgroundColor = '#1d4ed8'
                }
              }}
              onMouseLeave={(e) => {
                if (filtro !== 'deposito') {
                  e.currentTarget.style.backgroundColor = 'rgb(100, 116, 139)'
                  e.currentTarget.style.color = '#000000'
                } else {
                  e.currentTarget.style.backgroundColor = '#2563eb'
                }
              }}
            >
              Depósitos
            </button>
            <button
              onClick={() => setFiltro('bilhete')}
              style={{
                backgroundColor: filtro === 'bilhete' ? '#2563eb' : 'rgb(100, 116, 139)',
                color: filtro === 'bilhete' ? '#ffffff' : '#000000',
                border: filtro === 'bilhete' ? 'none' : '1px solid #64748b',
                padding: '6px 12px',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (filtro !== 'bilhete') {
                  e.currentTarget.style.backgroundColor = '#475569'
                  e.currentTarget.style.color = '#ffffff'
                } else {
                  e.currentTarget.style.backgroundColor = '#1d4ed8'
                }
              }}
              onMouseLeave={(e) => {
                if (filtro !== 'bilhete') {
                  e.currentTarget.style.backgroundColor = 'rgb(100, 116, 139)'
                  e.currentTarget.style.color = '#000000'
                } else {
                  e.currentTarget.style.backgroundColor = '#2563eb'
                }
              }}
            >
              Bilhetes
            </button>
            <button
              onClick={() => setFiltro('saque')}
              style={{
                backgroundColor: filtro === 'saque' ? '#2563eb' : 'rgb(100, 116, 139)',
                color: filtro === 'saque' ? '#ffffff' : '#000000',
                border: filtro === 'saque' ? 'none' : '1px solid #64748b',
                padding: '6px 12px',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                if (filtro !== 'saque') {
                  e.currentTarget.style.backgroundColor = '#475569'
                  e.currentTarget.style.color = '#ffffff'
                } else {
                  e.currentTarget.style.backgroundColor = '#1d4ed8'
                }
              }}
              onMouseLeave={(e) => {
                if (filtro !== 'saque') {
                  e.currentTarget.style.backgroundColor = 'rgb(100, 116, 139)'
                  e.currentTarget.style.color = '#000000'
                } else {
                  e.currentTarget.style.backgroundColor = '#2563eb'
                }
              }}
            >
              Saques
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-96 bg-slate-800">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Clock className="h-6 w-6 animate-spin text-blue-400 mr-2" />
              <span className="text-slate-300">Carregando pagamentos...</span>
            </div>
          ) : pagamentosFiltrados.length === 0 ? (
            <div className="text-center py-8">
              <CreditCard className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-300">Nenhum pagamento encontrado</p>
            </div>
          ) : (
            <div className="space-y-4">
              {pagamentosFiltrados.map((pagamento) => (
                <div key={pagamento.id} className="bg-slate-700 border border-slate-600 rounded-xl p-4 hover:bg-slate-650 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      {getTipoIcon(pagamento.tipo)}
                      <div className="flex-1">
                        <h3 className="font-medium text-white">
                          {pagamento.descricao}
                        </h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <Calendar className="h-3 w-3 text-slate-400" />
                          <span className="text-xs text-slate-400">
                            {pagamento.data}
                          </span>
                          {pagamento.transactionId && (
                            <>
                              <span className="text-slate-500">•</span>
                              <span className="text-xs text-slate-400">
                                ID: {pagamento.transactionId}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-2 mb-1">
                        {getStatusIcon(pagamento.status)}
                        {getStatusBadge(pagamento.status)}
                      </div>
                      <div className="text-lg font-bold text-white">
                        R$ {pagamento.valor.toFixed(2).replace('.', ',')}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-slate-600 bg-slate-700">
          <div
            onClick={onClose}
            style={{
              backgroundColor: '#4b5563',
              color: '#ffffff',
              border: 'none',
              minHeight: '44px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              width: '100%',
              padding: '12px',
              borderRadius: '6px',
              transition: 'all 0.2s ease',
              outline: 'none',
              boxShadow: 'none',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              userSelect: 'none'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#374151'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = '#4b5563'
            }}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                onClose()
              }
            }}
          >
            Fechar
          </div>
        </div>
      </div>
    </div>
  )
}

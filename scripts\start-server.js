#!/usr/bin/env node

import { spawn, exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

async function killProcessOnPort(port) {
  try {
    console.log(`🔍 Verificando processos na porta ${port}...`)
    
    // Para Windows (PowerShell)
    if (process.platform === 'win32') {
      try {
        const { stdout } = await execAsync(`netstat -ano | findstr :${port}`)
        if (stdout) {
          const lines = stdout.split('\n').filter(line => line.includes(`:${port}`))
          for (const line of lines) {
            const parts = line.trim().split(/\s+/)
            const pid = parts[parts.length - 1]
            if (pid && pid !== '0') {
              console.log(`🔪 Matando processo PID ${pid} na porta ${port}`)
              await execAsync(`taskkill /PID ${pid} /F`)
            }
          }
        }
      } catch (error) {
        // Processo não encontrado, tudo bem
      }
    } else {
      // Para Linux/Mac
      try {
        const { stdout } = await execAsync(`lsof -ti:${port}`)
        if (stdout) {
          const pids = stdout.trim().split('\n')
          for (const pid of pids) {
            if (pid) {
              console.log(`🔪 Matando processo PID ${pid} na porta ${port}`)
              await execAsync(`kill -9 ${pid}`)
            }
          }
        }
      } catch (error) {
        // Processo não encontrado, tudo bem
      }
    }
    
    console.log(`✅ Porta ${port} liberada`)
  } catch (error) {
    console.log(`⚠️ Erro ao liberar porta ${port}:`, error.message)
  }
}

async function startServer() {
  try {
    // Liberar porta 3000 se estiver ocupada
    await killProcessOnPort(3000)
    
    // Aguardar um pouco para garantir que a porta foi liberada
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    console.log('🚀 Iniciando servidor Next.js na porta 3000...')

    // Iniciar o servidor Next.js na porta 3000
    const server = spawn('npm', ['run', 'start'], {
      stdio: 'inherit',
      shell: true,
      env: {
        ...process.env,
        PORT: '3000'
      }
    })
    
    server.on('error', (error) => {
      console.error('❌ Erro ao iniciar servidor:', error)
      process.exit(1)
    })
    
    server.on('close', (code) => {
      console.log(`🔚 Servidor encerrado com código ${code}`)
      process.exit(code)
    })
    
    // Capturar sinais para encerrar graciosamente
    process.on('SIGINT', () => {
      console.log('\n🛑 Encerrando servidor...')
      server.kill('SIGINT')
    })
    
    process.on('SIGTERM', () => {
      console.log('\n🛑 Encerrando servidor...')
      server.kill('SIGTERM')
    })
    
  } catch (error) {
    console.error('❌ Erro:', error)
    process.exit(1)
  }
}

startServer()

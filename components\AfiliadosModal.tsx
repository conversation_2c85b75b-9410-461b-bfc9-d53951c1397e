'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  X,
  UserPlus,
  Users,
  DollarSign,
  Share2,
  Copy,
  Check,
  TrendingUp,
  Calendar,
  Link as LinkIcon,
  Wallet,
  History
} from 'lucide-react'
import { toast } from 'sonner'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface Afiliado {
  id: number
  nome: string
  email: string
  dataRegistro: string
  totalApostado: number
  comissaoGerada: number
  status: 'ativo' | 'inativo'
}

interface AfiliadosModalProps {
  isOpen: boolean
  onClose: () => void
  usuario: Usuario
}

export function AfiliadosModal({ isOpen, onClose, usuario }: AfiliadosModalProps) {
  const [activeTab, setActiveTab] = useState<'link' | 'historico'>('link')
  const [afiliados, setAfiliados] = useState<Afiliado[]>([])
  const [loading, setLoading] = useState(false)
  const [linkCopiado, setLinkCopiado] = useState(false)
  const [estatisticas, setEstatisticas] = useState({
    totalAfiliados: 0,
    comissaoTotal: 0,
    comissaoMes: 0,
    afiliadosAtivos: 0
  })

  const linkAfiliado = `https://bolao.com/ref/${usuario.id}`

  useEffect(() => {
    if (isOpen) {
      carregarAfiliados()
    }
  }, [isOpen])

  const carregarAfiliados = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/afiliados`)
      const data = await response.json()

      if (data.success && data.afiliados) {
        const afiliadosFormatados: Afiliado[] = data.afiliados.map((afiliado: any) => ({
          id: afiliado.id,
          nome: afiliado.nome,
          email: afiliado.email,
          dataRegistro: new Date(afiliado.data_cadastro || afiliado.created_at).toLocaleDateString('pt-BR'),
          totalApostado: parseFloat(afiliado.total_apostado || 0),
          comissaoGerada: parseFloat(afiliado.comissao_gerada || 0),
          status: afiliado.status
        }))

        setAfiliados(afiliadosFormatados)

        const stats = {
          totalAfiliados: afiliadosFormatados.length,
          comissaoTotal: afiliadosFormatados.reduce((sum, a) => sum + a.comissaoGerada, 0),
          comissaoMes: data.comissao_mes || 0,
          afiliadosAtivos: afiliadosFormatados.filter(a => a.status === 'ativo').length
        }

        setEstatisticas(stats)
      } else {
        console.error('Erro ao carregar afiliados:', data.error)
        setAfiliados([])
        setEstatisticas({
          totalAfiliados: 0,
          comissaoTotal: 0,
          comissaoMes: 0,
          afiliadosAtivos: 0
        })
      }
    } catch (error) {
      console.error('Erro ao carregar afiliados:', error)
      setAfiliados([])
      setEstatisticas({
        totalAfiliados: 0,
        comissaoTotal: 0,
        comissaoMes: 0,
        afiliadosAtivos: 0
      })
    } finally {
      setLoading(false)
    }
  }

  const copiarLink = async () => {
    try {
      await navigator.clipboard.writeText(linkAfiliado)
      setLinkCopiado(true)
      toast.success('Link copiado para a área de transferência!')
      
      setTimeout(() => {
        setLinkCopiado(false)
      }, 2000)
    } catch (error) {
      toast.error('Erro ao copiar link')
    }
  }

  const compartilharLink = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Venha apostar comigo!',
          text: 'Cadastre-se usando meu link e ganhe bônus especiais!',
          url: linkAfiliado
        })
      } catch (error) {
        console.log('Compartilhamento cancelado')
      }
    } else {
      copiarLink()
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-800 rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden border border-slate-600">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-slate-600">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-purple-600 rounded-xl flex items-center justify-center">
              <UserPlus className="h-5 w-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Meus Afiliados</h2>
              <p className="text-sm text-slate-300">
                {usuario.nome} - Saldo: R$ {usuario.saldo.toFixed(2).replace('.', ',')}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-slate-400 hover:text-white hover:bg-slate-600"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-slate-600">
          <button
            onClick={() => setActiveTab('link')}
            className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
              activeTab === 'link'
                ? 'text-purple-400 border-b-2 border-purple-400 bg-slate-750'
                : 'text-slate-400 hover:text-white hover:bg-slate-700'
            }`}
          >
            <UserPlus className="h-4 w-4 inline mr-2" />
            Convidar Afiliados
          </button>
          <button
            onClick={() => setActiveTab('historico')}
            className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
              activeTab === 'historico'
                ? 'text-purple-400 border-b-2 border-purple-400 bg-slate-750'
                : 'text-slate-400 hover:text-white hover:bg-slate-700'
            }`}
          >
            <History className="h-4 w-4 inline mr-2" />
            Histórico ({afiliados.length})
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-96 bg-slate-800">
          {activeTab === 'link' ? (
            <div className="space-y-6">
              {/* Estatísticas Resumidas */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-slate-700 rounded-xl p-4 border border-slate-600">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-slate-400 text-sm">Total Afiliados</p>
                      <p className="text-white text-2xl font-bold">{estatisticas.totalAfiliados}</p>
                    </div>
                    <Users className="h-8 w-8 text-purple-400" />
                  </div>
                </div>
                <div className="bg-slate-700 rounded-xl p-4 border border-slate-600">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-slate-400 text-sm">Comissão Total</p>
                      <p className="text-green-400 text-2xl font-bold">
                        R$ {estatisticas.comissaoTotal.toFixed(2).replace('.', ',')}
                      </p>
                    </div>
                    <DollarSign className="h-8 w-8 text-green-400" />
                  </div>
                </div>
              </div>

              {/* Link de Convite */}
              <div className="bg-slate-700 rounded-xl p-5 border border-slate-600">
                <h3 className="text-white font-semibold mb-3 flex items-center">
                  <LinkIcon className="h-5 w-5 mr-2 text-purple-400" />
                  Seu Link de Convite
                </h3>
                <div className="flex space-x-3 mb-4">
                  <Input
                    value={linkAfiliado}
                    readOnly
                    className="flex-1 bg-slate-600 border-slate-500 text-white"
                  />
                  <Button
                    onClick={copiarLink}
                    variant="outline"
                    className="border-slate-500 text-slate-300 hover:bg-slate-600"
                  >
                    {linkCopiado ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </div>
                <Button
                  onClick={compartilharLink}
                  className="w-full bg-purple-600 hover:bg-purple-700"
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  Compartilhar Link
                </Button>
                <p className="text-slate-400 text-sm mt-3 text-center">
                  Ganhe <span className="text-green-400 font-semibold">5%</span> de comissão sobre todas as apostas!
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <UserPlus className="h-6 w-6 animate-spin text-purple-400 mr-2" />
                  <span className="text-slate-300">Carregando...</span>
                </div>
              ) : afiliados.length === 0 ? (
                <div className="text-center py-8">
                  <UserPlus className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <p className="text-slate-300">Nenhum afiliado ainda</p>
                  <p className="text-slate-400 text-sm">Compartilhe seu link para começar!</p>
                </div>
              ) : (
                afiliados.map((afiliado) => (
                  <div key={afiliado.id} className="bg-slate-700 rounded-xl p-4 border border-slate-600">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
                          <span className="text-white font-bold">
                            {afiliado.nome.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <h4 className="text-white font-medium">{afiliado.nome}</h4>
                            <Badge className={afiliado.status === 'ativo' ? 'bg-green-600' : 'bg-slate-600'}>
                              {afiliado.status === 'ativo' ? 'Ativo' : 'Inativo'}
                            </Badge>
                          </div>
                          <p className="text-slate-400 text-sm">{afiliado.email}</p>
                          <p className="text-slate-500 text-xs">Desde {afiliado.dataRegistro}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-slate-400 text-sm">Comissão</p>
                        <p className="text-green-400 font-bold">
                          R$ {afiliado.comissaoGerada.toFixed(2).replace('.', ',')}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-slate-600 bg-slate-700">
          <Button
            onClick={onClose}
            className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-3"
            style={{
              backgroundColor: '#4b5563',
              color: '#ffffff',
              border: 'none',
              minHeight: '44px'
            }}
          >
            Fechar
          </Button>
        </div>
      </div>
    </div>
  )
}

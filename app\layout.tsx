import type { Metadata } from 'next'
import './globals.css'
import { Toaster } from 'sonner'
import { AutoSyncProvider } from '@/components/AutoSyncProvider'

export const metadata: Metadata = {
  title: 'Sistema Bolão - Apostas Esportivas',
  description: 'Sistema completo de bolão esportivo com apostas em tempo real',
  generator: 'Sistema Bolão',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="pt-BR">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <meta name="theme-color" content="#16a34a" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      </head>
      <body className="overflow-x-hidden">
        <AutoSyncProvider>
          {children}
          <Toaster />
        </AutoSyncProvider>
        {process.env.NODE_ENV === 'development' && (
          <script src="/test-modal.js" defer></script>
        )}
      </body>
    </html>
  )
}

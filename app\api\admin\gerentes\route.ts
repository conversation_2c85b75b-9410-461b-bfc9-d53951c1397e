import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"
import bcrypt from 'bcryptjs'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    await initializeDatabase()

    // Buscar gerentes com estatísticas
    const gerentes = await executeQuery(`
      SELECT 
        u.id,
        u.nome,
        u.email,
        u.telefone,
        u.status,
        u.data_cadastro as data_criacao,
        u.tipo as nivel_acesso,
        COUNT(DISTINCT uf.id) as total_usuarios,
        COALESCE(SUM(c.valor), 0) as comissao_total
      FROM usuarios u
      LEFT JOIN usuarios uf ON u.id = uf.gerente_id
      LEFT JOIN comissoes c ON u.id = c.gerente_id
      WHERE u.tipo IN ('gerente', 'supervisor', 'admin')
      GROUP BY u.id, u.nome, u.email, u.telefone, u.status, u.data_cadastro, u.tipo
      ORDER BY u.data_cadastro DESC
    `)

    const gerentesFormatados = gerentes.map((gerente: any) => ({
      id: gerente.id,
      nome: gerente.nome,
      email: gerente.email,
      telefone: gerente.telefone || "",
      status: gerente.status,
      data_criacao: gerente.data_criacao,
      total_usuarios: parseInt(gerente.total_usuarios || 0),
      comissao_total: parseFloat(gerente.comissao_total || 0),
      nivel_acesso: gerente.nivel_acesso
    }))

    return NextResponse.json({
      success: true,
      gerentes: gerentesFormatados
    })

  } catch (error) {
    console.error("❌ Erro ao buscar gerentes:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      gerentes: []
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { nome, email, telefone, senha, nivel_acesso } = body

    if (!nome || !email || !senha || !nivel_acesso) {
      return NextResponse.json({
        success: false,
        error: "Nome, email, senha e nível de acesso são obrigatórios"
      }, { status: 400 })
    }

    await initializeDatabase()

    // Verificar se email já existe
    const existingUser = await executeQuery(
      "SELECT id FROM usuarios WHERE email = ?",
      [email]
    )

    if (existingUser.length > 0) {
      return NextResponse.json({
        success: false,
        error: "Email já está em uso"
      }, { status: 400 })
    }

    // Hash da senha
    const senhaHash = await bcrypt.hash(senha, 10)

    // Inserir novo gerente
    const result = await executeQuery(`
      INSERT INTO usuarios (
        nome, email, telefone, senha_hash, tipo, status, data_cadastro
      ) VALUES (?, ?, ?, ?, ?, 'ativo', NOW())
    `, [nome, email, telefone, senhaHash, nivel_acesso])

    return NextResponse.json({
      success: true,
      message: "Gerente criado com sucesso",
      id: (result as any).insertId
    })

  } catch (error) {
    console.error("❌ Erro ao criar gerente:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor"
    }, { status: 500 })
  }
}

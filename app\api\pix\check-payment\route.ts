import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    const { bilhete_codigo, transaction_id } = await request.json()

    if (!bilhete_codigo && !transaction_id) {
      return NextResponse.json({
        success: false,
        error: "bilhete_codigo ou transaction_id é obrigatório"
      }, { status: 400 })
    }

    await initializeDatabase()

    // Buscar bilhete atual
    const bilhete = await executeQuery(`
      SELECT id, codigo, status, transaction_id, pix_order_id, valor_total
      FROM bilhetes 
      WHERE codigo = ? OR transaction_id = ? OR pix_order_id = ?
      LIMIT 1
    `, [bilhete_codigo || transaction_id, transaction_id || bilhete_codigo, transaction_id || bilhete_codigo])

    if (!Array.isArray(bilhete) || bilhete.length === 0) {
      return NextResponse.json({
        success: false,
        error: "Bilhete não encontrado",
        found: false
      })
    }

    const bilheteData = bilhete[0] as any

    // Se já está pago, retornar sucesso
    if (bilheteData.status === 'pago') {
      return NextResponse.json({
        success: true,
        status: 'pago',
        bilhete: {
          codigo: bilheteData.codigo,
          valor: bilheteData.valor_total,
          transaction_id: bilheteData.transaction_id
        },
        payment_confirmed: true
      })
    }

    // Se ainda está pendente, tentar verificar via webhook
    if (bilheteData.status === 'pendente') {
      try {
        console.log(`🔍 Verificando pagamento via webhook para: ${bilheteData.codigo}`)

        // Simular webhook para verificar pagamento
        const webhookResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/v1/MP/webhookruntransation`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            order_id: bilheteData.transaction_id || bilheteData.codigo,
            status: 'PAID',
            type: 'PIXOUT',
            message: 'Payment check'
          })
        })

        if (webhookResponse.ok) {
          const webhookResult = await webhookResponse.json()
          
          if (webhookResult.processed_successfully) {
            console.log(`✅ Pagamento confirmado para bilhete: ${bilheteData.codigo}`)
            
            return NextResponse.json({
              success: true,
              status: 'pago',
              bilhete: {
                codigo: bilheteData.codigo,
                valor: bilheteData.valor_total,
                transaction_id: bilheteData.transaction_id
              },
              payment_confirmed: true,
              just_confirmed: true
            })
          }
        }
      } catch (error) {
        console.error('❌ Erro ao verificar webhook:', error)
      }
    }

    // Retornar status atual
    return NextResponse.json({
      success: true,
      status: bilheteData.status,
      bilhete: {
        codigo: bilheteData.codigo,
        valor: bilheteData.valor_total,
        transaction_id: bilheteData.transaction_id
      },
      payment_confirmed: false
    })

  } catch (error) {
    console.error("❌ Erro ao verificar pagamento:", error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 })
  }
}

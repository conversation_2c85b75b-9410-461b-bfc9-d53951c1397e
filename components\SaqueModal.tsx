'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  X,
  ArrowDownCircle,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Clock,
  CreditCard
} from 'lucide-react'
import { toast } from 'sonner'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface SaqueModalProps {
  isOpen: boolean
  onClose: () => void
  usuario: Usuario
  onSaldoUpdate?: (novoSaldo: number) => void
}

export function SaqueModal({ isOpen, onClose, usuario, onSaldoUpdate }: SaqueModalProps) {
  const [valor, setValor] = useState('')
  const [chavePix, setChavePix] = useState('')
  const [tipoChave, setTipoChave] = useState<'cpf' | 'cnpj' | 'email' | 'telefone' | 'aleatoria'>('cpf')
  const [loading, setLoading] = useState(false)

  if (!isOpen) return null

  const handleSaque = async () => {
    try {
      setLoading(true)

      const valorNumerico = parseFloat(valor.replace(',', '.'))
      
      if (!valorNumerico || valorNumerico <= 0) {
        toast.error('Digite um valor válido')
        return
      }

      if (valorNumerico > usuario.saldo) {
        toast.error('Saldo insuficiente')
        return
      }

      if (!chavePix.trim()) {
        toast.error('Digite a chave PIX')
        return
      }

      // Simular processamento do saque
      await new Promise(resolve => setTimeout(resolve, 2000))

      toast.success('Solicitação de saque enviada! Será processada em até 24h.')
      
      // Atualizar saldo (descontar o valor)
      const novoSaldo = usuario.saldo - valorNumerico
      onSaldoUpdate?.(novoSaldo)
      
      // Limpar formulário
      setValor('')
      setChavePix('')
      setTipoChave('cpf')
      
      onClose()
    } catch (error) {
      console.error('Erro ao processar saque:', error)
      toast.error('Erro ao processar saque')
    } finally {
      setLoading(false)
    }
  }

  const formatarValor = (value: string) => {
    const numero = value.replace(/\D/g, '')
    const valorFormatado = (parseInt(numero) / 100).toFixed(2)
    return valorFormatado === '0.00' ? '' : valorFormatado.replace('.', ',')
  }

  const handleValorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const valorFormatado = formatarValor(e.target.value)
    setValor(valorFormatado)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 sm:p-6">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-sm mx-4 sm:max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2">
            <ArrowDownCircle className="h-5 w-5 text-orange-600" />
            <h2 className="text-lg font-semibold text-gray-900">
              Sacar Dinheiro
            </h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
          {/* Saldo Disponível */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  <span className="text-sm text-gray-600">Saldo Disponível</span>
                </div>
                <span className="text-lg font-bold text-green-600">
                  R$ {usuario.saldo.toFixed(2).replace('.', ',')}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Valor do Saque */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Valor do Saque
            </label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                R$
              </span>
              <Input
                type="text"
                value={valor}
                onChange={handleValorChange}
                placeholder="0,00"
                className="pl-8"
                disabled={loading}
              />
            </div>
          </div>

          {/* Tipo de Chave PIX */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Tipo de Chave PIX
            </label>
            <select
              value={tipoChave}
              onChange={(e) => setTipoChave(e.target.value as any)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={loading}
            >
              <option value="cpf">CPF</option>
              <option value="cnpj">CNPJ</option>
              <option value="email">E-mail</option>
              <option value="telefone">Telefone</option>
              <option value="aleatoria">Chave Aleatória</option>
            </select>
          </div>

          {/* Chave PIX */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Chave PIX
            </label>
            <Input
              type="text"
              value={chavePix}
              onChange={(e) => setChavePix(e.target.value)}
              placeholder={
                tipoChave === 'cpf' ? '000.000.000-00' :
                tipoChave === 'cnpj' ? '00.000.000/0000-00' :
                tipoChave === 'email' ? '<EMAIL>' :
                tipoChave === 'telefone' ? '(11) 99999-9999' :
                'Chave aleatória'
              }
              disabled={loading}
            />
          </div>

          {/* Aviso */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-xs text-yellow-800">
                <p className="font-medium">Importante:</p>
                <ul className="mt-1 space-y-1">
                  <li>• Saques são processados em até 24 horas úteis</li>
                  <li>• Valor mínimo: R$ 10,00</li>
                  <li>• Taxa: R$ 2,00 por saque</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Botões */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={loading}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSaque}
              className="flex-1 bg-orange-600 hover:bg-orange-700"
              disabled={loading || !valor || !chavePix}
            >
              {loading ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Processando...
                </>
              ) : (
                <>
                  <ArrowDownCircle className="h-4 w-4 mr-2" />
                  Solicitar Saque
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

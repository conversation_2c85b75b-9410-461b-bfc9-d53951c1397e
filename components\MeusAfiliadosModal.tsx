'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  X,
  UserPlus,
  Users,
  DollarSign,
  Share2,
  Copy,
  Check,
  TrendingUp,
  Calendar,
  Link as LinkIcon,
  Wallet
} from 'lucide-react'
import { toast } from 'sonner'

interface Usuario {
  id: number
  nome: string
  email: string
  saldo: number
}

interface Afiliado {
  id: number
  nome: string
  email: string
  dataRegistro: string
  totalApostado: number
  comissaoGerada: number
  status: 'ativo' | 'inativo'
}

interface MeusAfiliadosModalProps {
  isOpen: boolean
  onClose: () => void
  usuario: Usuario
}

export function MeusAfiliadosModal({ isOpen, onClose, usuario }: MeusAfiliadosModalProps) {
  const [afiliados, setAfiliados] = useState<Afiliado[]>([])
  const [loading, setLoading] = useState(false)
  const [linkCopiado, setLinkCopiado] = useState(false)
  const [estatisticas, setEstatisticas] = useState({
    totalAfiliados: 0,
    comissaoTotal: 0,
    comissaoMes: 0,
    afiliadosAtivos: 0
  })

  const linkAfiliado = `https://bolao.com/ref/${usuario.id}`

  useEffect(() => {
    if (isOpen) {
      carregarAfiliados()
    }
  }, [isOpen])

  const carregarAfiliados = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/user/afiliados?user_id=${usuario.id}`)
      const data = await response.json()

      if (data.success && data.afiliados) {
        const afiliadosFormatados: Afiliado[] = data.afiliados.map((afiliado: any) => ({
          id: afiliado.id,
          nome: afiliado.nome,
          email: afiliado.email,
          dataRegistro: new Date(afiliado.data_cadastro || afiliado.created_at).toLocaleString('pt-BR'),
          totalApostado: parseFloat(afiliado.total_apostado || 0),
          comissaoGerada: parseFloat(afiliado.comissao_gerada || 0),
          status: afiliado.status
        }))

        setAfiliados(afiliadosFormatados)

        // Calcular estatísticas
        const stats = {
          totalAfiliados: afiliadosFormatados.length,
          comissaoTotal: afiliadosFormatados.reduce((sum, a) => sum + a.comissaoGerada, 0),
          comissaoMes: data.comissao_mes || 0,
          afiliadosAtivos: afiliadosFormatados.filter(a => a.status === 'ativo').length
        }

        setEstatisticas(stats)
      } else {
        console.error('Erro ao carregar afiliados:', data.error)
        setAfiliados([])
        setEstatisticas({
          totalAfiliados: 0,
          comissaoTotal: 0,
          comissaoMes: 0,
          afiliadosAtivos: 0
        })
      }
    } catch (error) {
      console.error('Erro ao carregar afiliados:', error)
      setAfiliados([])
      setEstatisticas({
        totalAfiliados: 0,
        comissaoTotal: 0,
        comissaoMes: 0,
        afiliadosAtivos: 0
      })
    } finally {
      setLoading(false)
    }
  }

  const copiarLink = async () => {
    try {
      await navigator.clipboard.writeText(linkAfiliado)
      setLinkCopiado(true)
      toast.success('Link copiado para a área de transferência!')
      
      setTimeout(() => {
        setLinkCopiado(false)
      }, 2000)
    } catch (error) {
      toast.error('Erro ao copiar link')
    }
  }

  const compartilharLink = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Venha apostar comigo!',
          text: 'Cadastre-se usando meu link e ganhe bônus especiais!',
          url: linkAfiliado
        })
      } catch (error) {
        console.log('Compartilhamento cancelado')
      }
    } else {
      copiarLink()
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-slate-800 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden border border-slate-600">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-slate-600 bg-slate-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-purple-600 rounded-xl flex items-center justify-center">
              <UserPlus className="h-5 w-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">
                Meus Afiliados
              </h2>
              <p className="text-sm text-slate-300">
                {usuario.nome} - Saldo: R$ {usuario.saldo.toFixed(2).replace('.', ',')}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-slate-400 hover:text-white hover:bg-slate-600"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Estatísticas */}
        <div className="p-6 bg-gradient-to-r from-slate-700 to-slate-600 border-b border-slate-600">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-purple-600 rounded-xl mx-auto mb-3">
                <Users className="h-6 w-6 text-white" />
              </div>
              <div className="text-2xl font-bold text-white">{estatisticas.totalAfiliados}</div>
              <div className="text-sm text-slate-300">Total Afiliados</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-green-600 rounded-xl mx-auto mb-3">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div className="text-2xl font-bold text-white">{estatisticas.afiliadosAtivos}</div>
              <div className="text-sm text-slate-300">Ativos</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-yellow-600 rounded-xl mx-auto mb-3">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div className="text-2xl font-bold text-white">
                R$ {estatisticas.comissaoTotal.toFixed(2).replace('.', ',')}
              </div>
              <div className="text-sm text-slate-300">Comissão Total</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-blue-600 rounded-xl mx-auto mb-3">
                <Calendar className="h-6 w-6 text-white" />
              </div>
              <div className="text-2xl font-bold text-white">
                R$ {estatisticas.comissaoMes.toFixed(2).replace('.', ',')}
              </div>
              <div className="text-sm text-slate-300">Este Mês</div>
            </div>
          </div>
        </div>

        {/* Link de Afiliado */}
        <div className="p-6 border-b border-slate-600 bg-slate-750">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <LinkIcon className="h-5 w-5 mr-2 text-purple-400" />
            Seu Link de Afiliado
          </h3>
          <div className="flex space-x-3 mb-4">
            <Input
              value={linkAfiliado}
              readOnly
              className="flex-1 bg-slate-700 border-slate-600 text-white placeholder-slate-400"
            />
            <Button
              onClick={copiarLink}
              variant="outline"
              className="border-slate-600 text-slate-300 hover:bg-slate-600 hover:text-white flex items-center space-x-2"
            >
              {linkCopiado ? (
                <>
                  <Check className="h-4 w-4 text-green-400" />
                  <span>Copiado!</span>
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4" />
                  <span>Copiar</span>
                </>
              )}
            </Button>
            <Button
              onClick={compartilharLink}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              <Share2 className="h-4 w-4 mr-2" />
              Compartilhar
            </Button>
          </div>
          <div className="bg-slate-700 rounded-lg p-4 border border-slate-600">
            <p className="text-sm text-slate-300 flex items-center">
              <DollarSign className="h-4 w-4 mr-2 text-green-400" />
              Compartilhe este link e ganhe <strong className="text-green-400 mx-1">5%</strong> de comissão sobre todas as apostas dos seus afiliados!
            </p>
          </div>
        </div>

        {/* Lista de Afiliados */}
        <div className="p-6 overflow-y-auto max-h-96 bg-slate-800">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <UserPlus className="h-6 w-6 animate-spin text-purple-400 mr-2" />
              <span className="text-slate-300">Carregando afiliados...</span>
            </div>
          ) : afiliados.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-slate-700 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <UserPlus className="h-8 w-8 text-slate-400" />
              </div>
              <p className="text-slate-300 mb-2 text-lg font-medium">Você ainda não tem afiliados</p>
              <p className="text-sm text-slate-400">Compartilhe seu link para começar a ganhar comissões!</p>
            </div>
          ) : (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Users className="h-5 w-5 mr-2 text-purple-400" />
                Lista de Afiliados ({afiliados.length})
              </h3>
              {afiliados.map((afiliado) => (
                <div key={afiliado.id} className="bg-slate-700 border border-slate-600 rounded-xl p-5 hover:bg-slate-650 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center">
                        <span className="text-white font-bold text-lg">
                          {afiliado.nome.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="font-semibold text-white text-lg">{afiliado.nome}</h4>
                          <Badge
                            className={`text-xs px-2 py-1 rounded-full ${
                              afiliado.status === 'ativo'
                                ? 'bg-green-600 text-white'
                                : 'bg-slate-600 text-slate-300'
                            }`}
                          >
                            {afiliado.status === 'ativo' ? 'Ativo' : 'Inativo'}
                          </Badge>
                        </div>
                        <p className="text-slate-300 mb-2">{afiliado.email}</p>
                        <div className="text-sm text-slate-400 flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          Registrado em: {afiliado.dataRegistro}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-slate-300 mb-2">
                        Total Apostado: <span className="font-semibold text-white">R$ {afiliado.totalApostado.toFixed(2).replace('.', ',')}</span>
                      </div>
                      <div className="text-lg font-bold text-green-400 flex items-center justify-end">
                        <DollarSign className="h-4 w-4 mr-1" />
                        R$ {afiliado.comissaoGerada.toFixed(2).replace('.', ',')}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-slate-600 bg-slate-700">
          <Button
            onClick={onClose}
            className="w-full bg-slate-600 hover:bg-slate-500 text-white border-0"
          >
            Fechar
          </Button>
        </div>
      </div>
    </div>
  )
}

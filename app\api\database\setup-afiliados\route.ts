import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database.js"

export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    console.log('🏗️ Criando tabela de afiliados...')
    
    await initializeDatabase()
    
    // Criar tabela de afiliados
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS afiliados (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nome VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        telefone VARCHAR(20),
        codigo_afiliado VARCHAR(20) UNIQUE NOT NULL,
        percentual_comissao DECIMAL(5,2) DEFAULT 5.00,
        cpa_valor DECIMAL(10,2) DEFAULT 0.00,
        tipo_comissao ENUM('percentual', 'cpa') DEFAULT 'percentual',
        comissao_total DECIMAL(10,2) DEFAULT 0.00,
        total_indicacoes INT DEFAULT 0,
        senha_hash VARCHAR(255) NOT NULL,
        status ENUM('ativo', 'inativo', 'bloqueado') DEFAULT 'ativo',
        usuario_id INT NULL,
        data_cadastro TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_codigo_afiliado (codigo_afiliado),
        INDEX idx_status (status),
        INDEX idx_usuario_id (usuario_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ Tabela afiliados criada')

    // Criar tabela de indicações
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS afiliado_indicacoes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        afiliado_id INT NOT NULL,
        usuario_indicado_id INT NOT NULL,
        valor_comissao DECIMAL(10,2) DEFAULT 0.00,
        status ENUM('pendente', 'pago', 'cancelado') DEFAULT 'pendente',
        data_indicacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        data_pagamento TIMESTAMP NULL,
        INDEX idx_afiliado_id (afiliado_id),
        INDEX idx_usuario_indicado_id (usuario_indicado_id),
        INDEX idx_status (status),
        UNIQUE KEY unique_afiliado_usuario (afiliado_id, usuario_indicado_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ Tabela afiliado_indicacoes criada')

    // Criar alguns afiliados de exemplo
    const afiliadosExemplo = [
      {
        nome: 'João Silva',
        email: '<EMAIL>',
        telefone: '(11) 99999-1111',
        codigo_afiliado: 'AF001',
        percentual_comissao: 10.00,
        senha_hash: '$2b$10$exemplo_hash_senha'
      },
      {
        nome: 'Maria Santos',
        email: '<EMAIL>',
        telefone: '(11) 99999-2222',
        codigo_afiliado: 'AF002',
        percentual_comissao: 8.50,
        senha_hash: '$2b$10$exemplo_hash_senha'
      },
      {
        nome: 'Pedro Costa',
        email: '<EMAIL>',
        telefone: '(11) 99999-3333',
        codigo_afiliado: 'AF003',
        percentual_comissao: 12.00,
        senha_hash: '$2b$10$exemplo_hash_senha'
      },
      {
        nome: 'Ana Oliveira',
        email: '<EMAIL>',
        telefone: '(11) 99999-4444',
        codigo_afiliado: 'AF004',
        percentual_comissao: 15.00,
        tipo_comissao: 'cpa',
        cpa_valor: 25.00,
        senha_hash: '$2b$10$exemplo_hash_senha'
      }
    ]

    let afiliadosCriados = 0
    for (const afiliado of afiliadosExemplo) {
      try {
        await executeQuery(`
          INSERT IGNORE INTO afiliados (
            nome, email, telefone, codigo_afiliado, percentual_comissao, 
            cpa_valor, tipo_comissao, senha_hash, status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'ativo')
        `, [
          afiliado.nome,
          afiliado.email,
          afiliado.telefone,
          afiliado.codigo_afiliado,
          afiliado.percentual_comissao,
          afiliado.cpa_valor || 0.00,
          afiliado.tipo_comissao || 'percentual',
          afiliado.senha_hash
        ])
        afiliadosCriados++
        console.log(`✅ Afiliado criado: ${afiliado.nome} (${afiliado.codigo_afiliado})`)
      } catch (error) {
        console.log(`⚠️ Afiliado já existe: ${afiliado.nome}`)
      }
    }

    // Verificar estatísticas
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total_afiliados,
        COUNT(CASE WHEN status = 'ativo' THEN 1 END) as ativos,
        AVG(percentual_comissao) as media_comissao
      FROM afiliados
    `)

    const result = {
      success: true,
      message: 'Sistema de afiliados configurado com sucesso!',
      tabelas_criadas: ['afiliados', 'afiliado_indicacoes'],
      afiliados_criados: afiliadosCriados,
      stats: stats && stats[0] ? {
        total_afiliados: stats[0].total_afiliados,
        ativos: stats[0].ativos,
        media_comissao: parseFloat(stats[0].media_comissao || 0).toFixed(2)
      } : null
    }

    return NextResponse.json(result, { status: 200 })
    
  } catch (error) {
    console.error('❌ Erro ao criar tabelas de afiliados:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro ao criar sistema de afiliados',
      details: error.message
    }, { status: 500 })
  }
}

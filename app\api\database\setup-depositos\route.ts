import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database.js"

export const dynamic = 'force-dynamic'

export async function POST(request: NextRequest) {
  try {
    console.log('🏗️ Criando tabelas de depósitos PIX...')
    
    await initializeDatabase()
    
    // Criar tabela de depósitos PIX
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS depositos_pix (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        valor DECIMAL(10,2) NOT NULL,
        transaction_id VARCHAR(100) UNIQUE NOT NULL,
        pix_order_id VARCHAR(100),
        qr_code_value TEXT,
        qrcode_image LONGTEXT,
        status ENUM('pendente', 'pago', 'expirado', 'cancelado') DEFAULT 'pendente',
        expiration_datetime DATETIME,
        client_name VARCHAR(255),
        client_email VARCHAR(255),
        client_document VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_usuario_id (usuario_id),
        INDEX idx_transaction_id (transaction_id),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ Tabela depositos_pix criada')

    // Criar tabela de histórico de saldo
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS saldo_historico (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_id INT NOT NULL,
        tipo ENUM('deposito', 'saque', 'aposta', 'premio', 'bonus', 'estorno') NOT NULL,
        valor DECIMAL(10,2) NOT NULL,
        saldo_anterior DECIMAL(10,2) NOT NULL,
        saldo_novo DECIMAL(10,2) NOT NULL,
        descricao TEXT,
        referencia_id VARCHAR(100),
        referencia_tipo VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_usuario_id (usuario_id),
        INDEX idx_tipo (tipo),
        INDEX idx_created_at (created_at),
        INDEX idx_referencia (referencia_id, referencia_tipo)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `)
    console.log('✅ Tabela saldo_historico criada')

    // Criar alguns depósitos de exemplo para teste
    console.log('💰 Criando depósitos de exemplo...')
    
    const depositosExemplo = [
      {
        usuario_id: 1,
        valor: 50.00,
        transaction_id: `DEMO_${Date.now()}_1`,
        status: 'pago',
        client_name: 'Usuário Teste',
        client_email: '<EMAIL>',
        client_document: '12345678901'
      },
      {
        usuario_id: 1,
        valor: 25.00,
        transaction_id: `DEMO_${Date.now()}_2`,
        status: 'pendente',
        client_name: 'Usuário Teste',
        client_email: '<EMAIL>',
        client_document: '12345678901'
      }
    ]

    let depositosCriados = 0
    for (const deposito of depositosExemplo) {
      try {
        await executeQuery(`
          INSERT IGNORE INTO depositos_pix (
            usuario_id, valor, transaction_id, status, client_name, client_email, client_document
          ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
          deposito.usuario_id,
          deposito.valor,
          deposito.transaction_id,
          deposito.status,
          deposito.client_name,
          deposito.client_email,
          deposito.client_document
        ])
        depositosCriados++
        console.log(`✅ Depósito criado: R$ ${deposito.valor} (${deposito.status})`)
      } catch (error) {
        console.log(`⚠️ Depósito já existe: ${deposito.transaction_id}`)
      }
    }

    // Verificar estatísticas
    const stats = await executeQuery(`
      SELECT 
        COUNT(*) as total_depositos,
        COUNT(CASE WHEN status = 'pago' THEN 1 END) as pagos,
        COUNT(CASE WHEN status = 'pendente' THEN 1 END) as pendentes,
        SUM(CASE WHEN status = 'pago' THEN valor ELSE 0 END) as total_pago
      FROM depositos_pix
    `)

    const result = {
      success: true,
      message: 'Sistema de depósitos configurado com sucesso!',
      tabelas_criadas: ['depositos_pix', 'saldo_historico'],
      depositos_criados: depositosCriados,
      stats: stats && stats[0] ? {
        total_depositos: stats[0].total_depositos,
        pagos: stats[0].pagos,
        pendentes: stats[0].pendentes,
        total_pago: parseFloat(stats[0].total_pago || 0).toFixed(2)
      } : null
    }

    return NextResponse.json(result, { status: 200 })
    
  } catch (error) {
    console.error('❌ Erro ao criar tabelas de depósitos:', error)
    return NextResponse.json({
      success: false,
      error: 'Erro ao criar sistema de depósitos',
      details: error.message
    }, { status: 500 })
  }
}

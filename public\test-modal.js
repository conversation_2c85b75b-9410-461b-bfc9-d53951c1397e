// Script para testar o modal QR Code diretamente no navegador
console.log('🎯 Script de teste do Modal QR Code carregado!');

// Função para simular clique no botão de depósito
function testDepositModal() {
    console.log('🔄 Testando abertura do modal de depósito...');
    
    // Procurar pelo botão de depósito
    const depositButton = document.querySelector('[data-testid="deposit-button"]') || 
                         document.querySelector('button:contains("Depósito")') ||
                         Array.from(document.querySelectorAll('button')).find(btn => 
                             btn.textContent.includes('Depósito') || 
                             btn.textContent.includes('💰')
                         );
    
    if (depositButton) {
        console.log('✅ Botão de depósito encontrado!');
        depositButton.click();
        return true;
    } else {
        console.log('❌ Botão de depósito não encontrado');
        return false;
    }
}

// Função para simular geração de PIX
function testPixGeneration() {
    console.log('🔄 Testando geração de PIX...');
    
    // Aguardar modal abrir
    setTimeout(() => {
        // Procurar campo de valor
        const valueInput = document.querySelector('input[type="number"]') ||
                          document.querySelector('input[placeholder*="valor"]') ||
                          document.querySelector('input[placeholder*="Valor"]');
        
        if (valueInput) {
            console.log('✅ Campo de valor encontrado!');
            valueInput.value = '10';
            valueInput.dispatchEvent(new Event('input', { bubbles: true }));
            
            // Procurar botão Gerar PIX
            setTimeout(() => {
                const pixButton = Array.from(document.querySelectorAll('button')).find(btn => 
                    btn.textContent.includes('Gerar PIX') || 
                    btn.textContent.includes('PIX')
                );
                
                if (pixButton) {
                    console.log('✅ Botão Gerar PIX encontrado!');
                    pixButton.click();
                    
                    // Verificar se modal QR Code abriu
                    setTimeout(() => {
                        const qrModal = document.querySelector('[data-testid="qr-modal"]') ||
                                       document.querySelector('.qr-code-modal') ||
                                       Array.from(document.querySelectorAll('div')).find(div => 
                                           div.textContent.includes('Pagamento PIX') ||
                                           div.textContent.includes('QR Code')
                                       );
                        
                        if (qrModal) {
                            console.log('🎉 Modal QR Code aberto com sucesso!');
                            testCopyPasteFunction();
                        } else {
                            console.log('❌ Modal QR Code não encontrado');
                        }
                    }, 1000);
                } else {
                    console.log('❌ Botão Gerar PIX não encontrado');
                }
            }, 500);
        } else {
            console.log('❌ Campo de valor não encontrado');
        }
    }, 1000);
}

// Função para testar funcionalidade de copia e cola
function testCopyPasteFunction() {
    console.log('🔄 Testando funcionalidade de copia e cola...');
    
    // Procurar botão de copiar
    const copyButton = Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent.includes('Copiar') || 
        btn.textContent.includes('📋') ||
        btn.innerHTML.includes('Copy')
    );
    
    if (copyButton) {
        console.log('✅ Botão de copiar encontrado!');
        copyButton.click();
        
        // Verificar se código foi copiado
        setTimeout(() => {
            navigator.clipboard.readText().then(text => {
                if (text && text.length > 50) {
                    console.log('🎉 Código PIX copiado com sucesso!');
                    console.log('📋 Código:', text.substring(0, 50) + '...');
                } else {
                    console.log('❌ Código PIX não foi copiado');
                }
            }).catch(err => {
                console.log('⚠️ Não foi possível verificar área de transferência:', err);
            });
        }, 500);
    } else {
        console.log('❌ Botão de copiar não encontrado');
    }
}

// Função para executar teste completo
function runFullTest() {
    console.log('🚀 Iniciando teste completo do Modal QR Code...');
    
    if (testDepositModal()) {
        testPixGeneration();
    }
}

// Função para verificar se componentes estão carregados
function checkComponents() {
    console.log('🔍 Verificando componentes carregados...');
    
    const components = {
        'React': typeof React !== 'undefined',
        'Next.js': typeof window !== 'undefined' && window.next,
        'Wallet Modal': document.querySelector('.wallet-modal') !== null,
        'QR Code Modal': document.querySelector('.qr-code-modal') !== null,
        'Deposit Button': Array.from(document.querySelectorAll('button')).some(btn => 
            btn.textContent.includes('Depósito')
        )
    };
    
    console.table(components);
    return components;
}

// Exportar funções para uso no console
window.testModal = {
    runFullTest,
    testDepositModal,
    testPixGeneration,
    testCopyPasteFunction,
    checkComponents
};

console.log('📋 Funções disponíveis:');
console.log('- testModal.runFullTest() - Executa teste completo');
console.log('- testModal.testDepositModal() - Testa abertura do modal');
console.log('- testModal.testPixGeneration() - Testa geração de PIX');
console.log('- testModal.testCopyPasteFunction() - Testa copia e cola');
console.log('- testModal.checkComponents() - Verifica componentes');

// Auto-executar verificação de componentes
setTimeout(checkComponents, 2000);

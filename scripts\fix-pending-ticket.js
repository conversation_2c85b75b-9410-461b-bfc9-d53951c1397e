// Script para corrigir bilhete pendente
import fetch from 'node-fetch'

const API_BASE = 'http://localhost:3000'

async function fixPendingTicket() {
  try {
    console.log('🔍 Buscando bilhetes pendentes...')
    
    // Primeiro, buscar bilhetes pendentes
    const debugResponse = await fetch(`${API_BASE}/api/debug/bilhetes-pendentes`)
    const debugData = await debugResponse.json()
    
    console.log('📋 Bilhetes pendentes encontrados:', debugData.total_pendentes)
    
    if (debugData.bilhetes_pendentes && debugData.bilhetes_pendentes.length > 0) {
      const bilhete = debugData.bilhetes_pendentes[0]
      console.log('🎫 Bilhete a ser atualizado:', {
        id: bilhete.id,
        codigo: bilhete.codigo,
        transaction_id: bilhete.transaction_id,
        status: bilhete.status
      })
      
      // Atualizar o bilhete para pago
      console.log('💾 Atualizando bilhete para pago...')
      const updateResponse = await fetch(`${API_BASE}/api/debug/bilhetes-pendentes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          bilhete_id: bilhete.id,
          order_id: '5f238dd289044a8bb90ddb98d5e6e418',
          status: 'pago'
        })
      })
      
      const updateResult = await updateResponse.json()
      console.log('✅ Resultado da atualização:', updateResult)
      
      if (updateResult.success) {
        console.log('🎉 Bilhete atualizado com sucesso!')
        console.log('📊 Linhas afetadas:', updateResult.linhas_afetadas)
        
        // Testar o webhook agora
        console.log('🧪 Testando webhook com o order_id correto...')
        const webhookResponse = await fetch(`${API_BASE}/api/v1/MP/webhookruntransation`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            order_id: '5f238dd289044a8bb90ddb98d5e6e418',
            status: 'PAID',
            type: 'PIXOUT',
            message: 'Payment approved via manual fix'
          })
        })
        
        const webhookResult = await webhookResponse.json()
        console.log('📥 Resultado do webhook:', webhookResult)
        
      } else {
        console.log('❌ Falha ao atualizar bilhete:', updateResult.error)
      }
      
    } else {
      console.log('ℹ️ Nenhum bilhete pendente encontrado')
    }
    
  } catch (error) {
    console.error('❌ Erro ao corrigir bilhete:', error)
  }
}

// Executar correção
fixPendingTicket()

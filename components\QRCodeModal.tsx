'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  X,
  Copy,
  Check,
  QrCode,
  Clock,
  AlertCircle
} from 'lucide-react'
import { toast } from 'sonner'

interface QRCodeModalProps {
  isOpen: boolean
  onClose: () => void
  qrCodeData: {
    qrcode_image?: string
    qr_code_value?: string
    transaction_id?: string
    value?: number
    expiration_datetime?: string
  } | null
  onPaymentConfirmed?: () => void
}

export function QRCodeModal({ 
  isOpen, 
  onClose, 
  qrCodeData, 
  onPaymentConfirmed 
}: QRCodeModalProps) {
  const [copied, setCopied] = useState(false)
  const [timeLeft, setTimeLeft] = useState<number | null>(null)
  const [isExpired, setIsExpired] = useState(false)

  // Calcular tempo restante
  useEffect(() => {
    if (!qrCodeData?.expiration_datetime) return

    const updateTimer = () => {
      const now = new Date().getTime()
      const expiration = new Date(qrCodeData.expiration_datetime!).getTime()
      const difference = expiration - now

      if (difference > 0) {
        setTimeLeft(Math.floor(difference / 1000))
        setIsExpired(false)
      } else {
        setTimeLeft(0)
        setIsExpired(true)
      }
    }

    updateTimer()
    const interval = setInterval(updateTimer, 1000)

    return () => clearInterval(interval)
  }, [qrCodeData?.expiration_datetime])

  // Formatar tempo restante
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // Copiar código PIX
  const copyPixCode = async () => {
    if (!qrCodeData?.qr_code_value) {
      toast.error('Código PIX não disponível')
      return
    }

    try {
      await navigator.clipboard.writeText(qrCodeData.qr_code_value)
      setCopied(true)
      toast.success('Código PIX copiado!')
      
      setTimeout(() => {
        setCopied(false)
      }, 2000)
    } catch (error) {
      console.error('Erro ao copiar:', error)
      toast.error('Erro ao copiar código PIX')
    }
  }



  // Fechar modal com ESC
  useEffect(() => {
    const handleEsc = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEsc)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEsc)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  if (!isOpen || !qrCodeData) return null

  // Debug: verificar dados do QR Code
  console.log('🔍 Dados do QR Code no modal:', {
    qrcode_image: qrCodeData.qrcode_image ? 'Presente' : 'Ausente',
    qr_code_value: qrCodeData.qr_code_value ? 'Presente' : 'Ausente',
    value: qrCodeData.value,
    transaction_id: qrCodeData.transaction_id
  })

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 sm:p-6">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-sm mx-4 sm:max-w-md">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2">
            <QrCode className="h-5 w-5 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900">
              Pagamento PIX
            </h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-4 sm:p-6 space-y-4 sm:space-y-6">
          {/* Valor */}
          <div className="text-center">
            <p className="text-sm text-gray-600">Valor a pagar</p>
            <p className="text-2xl font-bold text-gray-900">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(qrCodeData.value || 0)}
            </p>
          </div>

          {/* Timer */}
          {timeLeft !== null && (
            <div className={`text-center p-3 rounded-lg ${
              isExpired 
                ? 'bg-red-50 border border-red-200' 
                : timeLeft < 300 
                  ? 'bg-yellow-50 border border-yellow-200'
                  : 'bg-blue-50 border border-blue-200'
            }`}>
              <div className="flex items-center justify-center space-x-2">
                {isExpired ? (
                  <AlertCircle className="h-4 w-4 text-red-600" />
                ) : (
                  <Clock className="h-4 w-4 text-blue-600" />
                )}
                <span className={`text-sm font-medium ${
                  isExpired ? 'text-red-700' : 'text-blue-700'
                }`}>
                  {isExpired ? 'PIX Expirado' : `Expira em: ${formatTime(timeLeft)}`}
                </span>
              </div>
            </div>
          )}

          {/* QR Code */}
          {qrCodeData.qrcode_image ? (
            <div className="text-center">
              <div className="inline-block p-3 sm:p-4 bg-white border-2 border-gray-200 rounded-lg">
                <img
                  src={
                    qrCodeData.qrcode_image.startsWith('data:')
                      ? qrCodeData.qrcode_image
                      : `data:image/png;base64,${qrCodeData.qrcode_image}`
                  }
                  alt="QR Code PIX"
                  className="w-40 h-40 sm:w-48 sm:h-48 mx-auto block"
                  style={{
                    backgroundColor: 'white'
                  }}
                  onLoad={() => {
                    console.log('✅ QR Code carregado com sucesso!');
                  }}
                  onError={(e) => {
                    console.error('❌ Erro ao carregar QR Code:', e);
                    console.log('📊 QR Code data length:', qrCodeData.qrcode_image?.length);
                    console.log('📊 QR Code start:', qrCodeData.qrcode_image?.substring(0, 50));
                    // Mostrar placeholder em caso de erro
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML = `
                        <div class="w-40 h-40 sm:w-48 sm:h-48 mx-auto flex items-center justify-center bg-gray-100 border-2 border-red-300 rounded text-red-600">
                          <div class="text-center">
                            <p class="text-sm font-medium">Erro ao carregar QR Code</p>
                            <p class="text-xs mt-1">Verifique o console para detalhes</p>
                          </div>
                        </div>
                      `;
                    }
                  }}
                />
              </div>
              <p className="text-xs text-gray-500 mt-2">
                Escaneie o QR Code com seu app de banco
              </p>
            </div>
          ) : (
            <div className="text-center">
              <div className="inline-block p-3 sm:p-4 bg-gray-100 border-2 border-gray-300 rounded-lg">
                <div className="w-40 h-40 sm:w-48 sm:h-48 mx-auto flex items-center justify-center text-gray-500">
                  <div className="text-center">
                    <p className="text-sm font-medium">QR Code não disponível</p>
                    <p className="text-xs mt-1">Tente gerar novamente</p>
                  </div>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                QR Code não foi gerado
              </p>
            </div>
          )}

          {/* Código PIX para copiar */}
          {qrCodeData.qr_code_value && (
            <div className="space-y-3">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                <label className="text-sm font-medium text-gray-700">
                  Código PIX (Copia e Cola)
                </label>
                <Button
                  onClick={copyPixCode}
                  size="sm"
                  variant="outline"
                  className="flex items-center justify-center space-x-1 w-full sm:w-auto bg-blue-50 text-blue-700 border-blue-300 hover:bg-blue-100"
                  style={{
                    backgroundColor: copied ? '#dcfce7' : '#dbeafe',
                    color: copied ? '#166534' : '#1d4ed8',
                    border: `1px solid ${copied ? '#bbf7d0' : '#93c5fd'}`
                  }}
                >
                  {copied ? (
                    <>
                      <Check className="h-3 w-3 text-green-600" />
                      <span className="text-green-600">Copiado!</span>
                    </>
                  ) : (
                    <>
                      <Copy className="h-3 w-3 text-blue-700" />
                      <span className="text-blue-700">Copiar</span>
                    </>
                  )}
                </Button>
              </div>
              
              <div className="relative">
                <Input
                  value={qrCodeData.qr_code_value}
                  readOnly
                  className="pr-12 text-xs sm:text-sm font-mono bg-gray-50 text-black"
                  style={{ color: '#000000' }}
                  onClick={copyPixCode}
                />
                <Button
                  onClick={copyPixCode}
                  size="icon"
                  variant="ghost"
                  className="absolute right-1 top-1 h-8 w-8 hover:bg-blue-100"
                  style={{
                    backgroundColor: copied ? '#dcfce7' : '#f8fafc',
                    color: copied ? '#166534' : '#475569'
                  }}
                >
                  {copied ? (
                    <Check className="h-3 w-3 text-green-600" />
                  ) : (
                    <Copy className="h-3 w-3 text-gray-600" />
                  )}
                </Button>
              </div>
            </div>
          )}



          {/* Botão de ação */}
          <div className="mt-4 sm:mt-6">
            <Button
              onClick={onClose}
              variant="outline"
              className="w-full bg-white text-gray-900 border-gray-300 hover:bg-gray-50 font-medium py-3"
              style={{
                backgroundColor: '#ffffff',
                color: '#111827',
                border: '1px solid #d1d5db',
                minHeight: '44px'
              }}
            >
              Fechar
            </Button>
          </div>

          {/* ID da transação */}
          {qrCodeData.transaction_id && (
            <div className="text-center pt-4 border-t">
              <p className="text-xs text-gray-500">
                ID: {qrCodeData.transaction_id}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
